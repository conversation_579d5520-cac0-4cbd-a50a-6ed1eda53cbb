<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\AuthorResource;
use App\Models\Audiobook;
use App\Models\Author;
use App\Services\SanitizerService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class AuthorsController extends Controller
{
    public SanitizerService $sanitizerService;

    public function __construct(SanitizerService $sanitizerService)
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ])->except([
            'index',
            'indexAudiobooks',
            'show',
        ]);

        $this->sanitizerService = $sanitizerService;
    }

    public function index(Request $request): Collection
    {
        $query = $request->get('query');

        if (empty($query)) {
            return Collection::empty();
        }

        return Author::where('name', 'like', '%' . $query . '%')
            ->limit(100)
            ->get()
            ->pluck('name');
    }

    public function indexAudiobooks(Request $request): Collection
    {
        $query = $request->get('query');

        if (empty($query)) {
            return Collection::empty();
        }

        return Audiobook::where('creator', 'like', '%' . $query . '%')
            ->get()
            ->unique('creator')
            ->pluck('creator');
    }

    public function show(Author $author, Request $request): array
    {
        return (new AuthorResource($author))->toArray($request);
    }

    public function follow(Author $author): array
    {
        return [
            'success' => $author->follow(),
        ];
    }

    public function unfollow(Author $author): array
    {
        return [
            'success' => $author->unfollow(),
        ];
    }

    public function followed(): array
    {
        $authorsFollowed = Auth::user()->authorsFollowed;

        return AuthorResource::collection($authorsFollowed)->jsonSerialize();
    }
}
