<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BookLike;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class LikesController extends Controller
{
    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
    }

    public function index(Request $request): Collection
    {
        $full = $request->get('full', true);
        $likes = Auth::user()->bookLikes;

        if (!$full) {
            $likes = $likes->take(5);
        }

        return $likes->map(fn (BookLike $bookLike) => $bookLike->book->toDTO());
    }

    public function like(Book $book): array
    {
        $success = $book->like();

        return [
            'success' => $success,
        ];
    }

    public function dislike(Book $book): array
    {
        $success = $book->dislike();

        return [
            'success' => $success,
        ];
    }

    public function removeLike(Book $book): array
    {
        $success = $book->removeLike();

        return [
            'success' => $success,
        ];
    }
}
