<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\BookReadStatusType;
use App\Models\Book;
use App\Models\BookReadStatus;
use App\Services\BookStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class BookReadStatusesController extends Controller
{
    private BookStatusService $bookStatusService;

    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
        $this->bookStatusService = new BookStatusService();
    }

    public function index(Request $request): array
    {
        return $this->bookStatusService->index($request);
    }

    public function show(string $type): Collection
    {
        $bookReadStatusType = BookReadStatusType::fromName($type);
        if ($bookReadStatusType === null) {
            abort(404, 'Book read status type not found');
        }

        $booksWithReadStatus = Auth::user()
            ->booksWithReadStatus()
            ->where('book_read_status_type_id', $bookReadStatusType->getId())
            ->get();

        return $booksWithReadStatus->map(
            fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book->toDTO()
        )->values();
    }

    public function setBookStatus(Book $book, Request $request): array
    {
        $statusType = $request->get('statusType');
        $bookReadStatusType = BookReadStatusType::fromName($statusType);
        if ($bookReadStatusType === null) {
            return [
                'success' => false,
            ];
        }

        $authId = Auth::id();
        $bookReadStatus = new BookReadStatus();
        $bookReadStatus->setStatusType($bookReadStatusType);
        $bookReadStatus->user_id = $authId;
        $book->readStatuses()->where('user_id', $authId)->delete();

        return [
            'success' => (bool)$book->readStatuses()->save($bookReadStatus),
        ];
    }

    public function removeBookStatus(Book $book): array
    {
        return [
            'success' => $book->readStatuses()->where('user_id', Auth::id())->delete() > 0,
        ];
    }
}
