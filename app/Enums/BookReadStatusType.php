<?php declare(strict_types=1);

namespace App\Enums;

enum BookReadStatusType: string
{
    case CURRENTLY_READING = 'currentlyReading';
    case READ = 'read';
    case WANT_TO_READ = 'wantToRead';
    case DROPPED = 'dropped';

    public function getId(): int
    {
        return match ($this) {
            self::CURRENTLY_READING => 1,
            self::READ => 2,
            self::WANT_TO_READ => 3,
            self::DROPPED => 4,
        };
    }

    public function getName(): string
    {
        return $this->value;
    }

    public static function fromId(int $id): ?self
    {
        return match ($id) {
            1 => self::CURRENTLY_READING,
            2 => self::READ,
            3 => self::WANT_TO_READ,
            4 => self::DROPPED,
            default => null,
        };
    }

    public static function fromName(string $name): ?self
    {
        return match ($name) {
            'currentlyReading' => self::CURRENTLY_READING,
            'read' => self::READ,
            'wantToRead' => self::WANT_TO_READ,
            'dropped' => self::DROPPED,
            default => null,
        };
    }

    public static function all(): array
    {
        return [
            self::CURRENTLY_READING,
            self::READ,
            self::WANT_TO_READ,
            self::DROPPED,
        ];
    }
}
