<?php declare(strict_types=1);

namespace App\Console\Commands\Recommendations;

use App\Models\User;
use App\Services\RecommendationsService;
use Illuminate\Console\Command;

final class GenerateRecommendationsCommand extends Command
{
    private const USER_CHUNK_SIZE = 10000;

    protected $signature = 'recommendations:generate';
    protected $description = 'Generate book basic recommendations for a specific user.';
    private RecommendationsService $recommendationsService;

    public function __construct(RecommendationsService $recommendationsService)
    {
        parent::__construct();
        $this->recommendationsService = $recommendationsService;
    }

    public function handle(): int
    {
        User::chunk(self::USER_CHUNK_SIZE, function ($users): void {
            foreach ($users as $user) {
                $this->recommendationsService->generateRecommendations($user);
            }
        });

        return Command::SUCCESS;
    }
}
