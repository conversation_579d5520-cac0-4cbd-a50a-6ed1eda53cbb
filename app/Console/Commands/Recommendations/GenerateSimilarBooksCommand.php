<?php declare(strict_types=1);

namespace App\Console\Commands\Recommendations;

use App\Models\Book;
use App\Services\SimilarBooksService;
use Illuminate\Console\Command;

final class GenerateSimilarBooksCommand extends Command
{
    private const BOOK_CHUNK_SIZE = 10000;

    protected $signature = 'recommendations:similar';
    protected $description = 'Generate similar books for books with ratings.';
    private SimilarBooksService $similarBooksService;

    public function __construct(SimilarBooksService $similarBooksService)
    {
        parent::__construct();
        $this->similarBooksService = $similarBooksService;
    }

    public function handle(): int
    {
        $count = 0;
        Book::with([
            'editions',
            'authors',
            'subjects',
        ])
            ->doesntHaveIn('similarBooks')
            ->whereHas('editions', function ($query): void {
                $query->where('google_ratings_count', '>', 0)
                    ->orWhere('open_library_ratings_count', '>', 0)
                    ->orWhere('open_library_reading_logs_count', '>', 0);
            })
            ->chunk(self::BOOK_CHUNK_SIZE, function ($books) use (&$count): void {
                foreach ($books as $book) {
                    $this->similarBooksService->generateSimilarBooks($book);
                }

                $count += self::BOOK_CHUNK_SIZE;
                $this->info("Similar books have been generated for {$count} books.");
            });

        $this->info('All similar books have been successfully generated.');

        return Command::SUCCESS;
    }
}
