<?php declare(strict_types=1);

namespace App\Services;

use App\Models\Book;
use Illuminate\Support\Facades\Config;

final class SimilarBooksService
{
    private const MAX_PAGE_NUMBER_DIFFERENCE_PERCENTAGE = 20;
    private const MAX_PUBLISH_YEAR_DIFFERENCE = 25;
    private const SIMILAR_BOOKS_MAX_COUNT = 4;

    public function generateSimilarBooks(Book $book): void
    {
        $similarBooks = $this->getSimilarBooks($book);
        $this->saveSimilarBooks($book, $similarBooks);
    }

    private function getSimilarBooks(Book $book): array
    {
        $edition = $book->getMainEdition();
        $scores = Config::get('recommendations.scores.similar_books');

        $similarBooks = [];
        Book::with([
            'editions',
            'authors',
            'subjects',
        ])
            ->where('id', '!=', $book->id)
            ->whereHas('editions', function ($query) use ($edition): void {
                $query->where('language_id', $edition->language_id);
            })
            ->where(function ($query) use ($book): void {
                $query->whereHas('authors', function ($query) use ($book): void {
                    $query->whereIn('authors.id', $book->authors->pluck('id'));
                })->orWhereHas('subjects', function ($query) use ($book): void {
                    $query->whereIn('subjects.id', $book->subjects->pluck('id'));
                });
            })
            ->get()
            ->each(function (Book $possibleSimilarBook) use ($book, $edition, &$similarBooks, $scores): void {
                $score = 0;
                $score += $scores['author'] * $possibleSimilarBook->authors->intersect($book->authors)->count();
                $score += $scores['subject'] * $possibleSimilarBook->subjects->intersect($book->subjects)->count();

                $possibleSimilarEdition = $possibleSimilarBook->getMainEdition();
                if (abs($possibleSimilarEdition->publish_year - $edition->publish_year) < self::MAX_PUBLISH_YEAR_DIFFERENCE) {
                    $score += $scores['publication_date'];
                }

                if (
                    abs($possibleSimilarEdition->number_of_pages - $edition->number_of_pages)
                    / max($possibleSimilarEdition->number_of_pages, $edition->number_of_pages)
                    < self::MAX_PAGE_NUMBER_DIFFERENCE_PERCENTAGE / 100
                ) {
                    $score += $scores['number_of_pages'];
                }

                if ($possibleSimilarEdition->publisher_id === $edition->publisher_id) {
                    $score += $scores['publisher'];
                }

                $similarBooks[] = [
                    'bookId' => $possibleSimilarBook->id,
                    'score' => $score,
                    'isFree' => $possibleSimilarEdition->google_id !== null,
                ];
            });

        usort($similarBooks, fn ($a, $b) => $a['score'] <=> $b['score']);
        $similarPaidBooks = array_filter($similarBooks, fn ($book) => !$book['isFree']);
        $similarFreeBooks = array_filter($similarBooks, fn ($book) => $book['isFree']);

        return [
            'paid' => array_slice($similarPaidBooks, 0, self::SIMILAR_BOOKS_MAX_COUNT),
            'free' => array_slice($similarFreeBooks, 0, self::SIMILAR_BOOKS_MAX_COUNT),
        ];
    }

    private function saveSimilarBooks(Book $book, array $similarBooks): void
    {
        $sync = [];
        foreach ($similarBooks['paid'] as $similarBook) {
            $sync[$similarBook['bookId']] = [
                'points' => $similarBook['score'],
            ];
        }
        $book->similarBooks()->sync($sync);

        $sync = [];
        foreach ($similarBooks['free'] as $similarBook) {
            $sync[$similarBook['bookId']] = [
                'points' => $similarBook['score'],
            ];
        }
        $book->similarFreeBooks()->sync($sync);
    }
}
