<?php declare(strict_types=1);

namespace App\Services;

use App\Models\Book;
use App\Models\BookReadStatusType;
use App\Models\Recommendation;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;

final class RecommendationsService
{
    public function generateRecommendations(User $user): void
    {
        $coefficients = Config::get('recommendations.scores.recommendations_coefficients');

        $possibleRecommendations = $this->getPossibleRecommendations($user->likedBooks(), $coefficients['like']);
        $recommendations = $this->addScoresToRecommendations([], $possibleRecommendations);

        $possibleRecommendations = $this->getPossibleRecommendations($user->sharedBooks(), $coefficients['share']);
        $recommendations = $this->addScoresToRecommendations($recommendations, $possibleRecommendations);

        $possibleRecommendations = $this->getPossibleRecommendations($user->finishedBooks(), $coefficients['finish']);
        $recommendations = $this->addScoresToRecommendations($recommendations, $possibleRecommendations);

        $possibleRecommendations = $this->getPossibleRecommendations($user->viewedBooks(), $coefficients['view']);
        $recommendations = $this->addScoresToRecommendations($recommendations, $possibleRecommendations);

        $recommendations = $this->excludeKnownBooks($recommendations, $user);
        $this->saveRecommendations($recommendations, $user);
    }

    private function getPossibleRecommendations(Collection $books, float $coefficient): array
    {
        $possibleRecommendations = [];

        $books->load('similarBooks')
            ->load('similarFreeBooks')
            ->each(function (Book $book) use (&$possibleRecommendations, $coefficient): void {
                $book->similarBooks->each(function (Book $similarBook) use (&$possibleRecommendations, $coefficient): void {
                    $possibleRecommendations[$similarBook->id] = $similarBook->pivot->points * $coefficient;
                });

                $book->similarFreeBooks->each(function (Book $similarBook) use (&$possibleRecommendations, $coefficient): void {
                    $possibleRecommendations[$similarBook->id] = $similarBook->pivot->points * $coefficient;
                });
            });

        return $possibleRecommendations;
    }

    private function addScoresToRecommendations(array $recommendations, array $possibleRecommendations): array
    {
        foreach ($possibleRecommendations as $bookId => $score) {
            if (!isset($recommendations[$bookId])) {
                $recommendations[$bookId] = 0;
            }

            $recommendations[$bookId] += $score;
        }

        return $recommendations;
    }

    private function excludeKnownBooks(array $recommendations, User $user): array
    {
        $user->bookLikesAndDislikes()->where('isPositive', 0)->get()
            ->map->book
            ->each(function (Book $book) use (&$recommendations): void {
                unset($recommendations[$book->id]);
            });

        $user->booksWithReadStatus()->whereNot('book_read_status_type_id', BookReadStatusType::WANT_TO_READ)->get()
            ->map->book
            ->each(function (Book $book) use (&$recommendations): void {
                unset($recommendations[$book->id]);
            });

        return $recommendations;
    }

    private function saveRecommendations(array $recommendations, User $user): void
    {
        $user->recommendations()->delete();

        arsort($recommendations);
        $recommendations = array_slice($recommendations, 0, 8, true);
        foreach ($recommendations as $bookId => $score) {
            $recommendation = new Recommendation();
            $recommendation->user_id = $user->id;
            $recommendation->book_id = $bookId;
            $recommendation->score = $score;

            $recommendation->save();
        }
    }
}
