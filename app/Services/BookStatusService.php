<?php declare(strict_types=1);

namespace App\Services;

use App\Enums\BookReadStatusType;
use App\Models\BookReadStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class BookStatusService
{
    public function index(Request $request): array
    {
        $full = $request->get('full', '1') === '1';

        return $this->getBooksGroupedByStatus(!$full);
    }

    public function getBooksGroupedByStatus(bool $isLimited = true): array
    {
        $userBooksWithReadStatus = Auth::user()->booksWithReadStatus;

        $bookReadStatuses = [];
        $bookReadStatusTypes = BookReadStatusType::all();
        foreach ($bookReadStatusTypes as $bookReadStatusType) {
            $bookReadStatuses[$bookReadStatusType->getName()] = $userBooksWithReadStatus->filter(
                fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book_read_status_type_id == $bookReadStatusType->getId()
            );

            if ($isLimited) {
                $bookReadStatuses[$bookReadStatusType->getName()] = $bookReadStatuses[$bookReadStatusType->getName()]->take(5);
            }

            $bookReadStatuses[$bookReadStatusType->getName()] = $bookReadStatuses[$bookReadStatusType->getName()]->map(
                fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book->toDTO()
            )->values();
        }

        return $bookReadStatuses;
    }
}
