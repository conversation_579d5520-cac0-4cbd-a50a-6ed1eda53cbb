<?php declare(strict_types=1);

namespace App\Models;

use App\Notifications\EmailVerificationNotification;
use App\Notifications\PasswordResetNotification;
use Illuminate\Auth\Passwords\CanResetPassword as CanResetPasswordTrait;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\User
 *
 * @property int $id
 * @property string $uuid
 * @property string $name
 * @property string $email
 * @property Carbon|null $email_verified_at
 * @property string $password
 * @property int $is_password_set
 * @property string|null $google_sub
 * @property string|null $apple_sub
 * @property string|null $profile_picture
 * @property string $language
 * @property bool $has_done_tutorial
 * @property string|null $register_ip
 * @property string|null $new_email
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class User extends Authenticatable implements MustVerifyEmail, CanResetPassword
{
    use HasApiTokens;
    use HasFactory;
    use Notifiable;
    use CanResetPasswordTrait;

    public const PROFILE_PICTURES_DIRECTORY = 'profile_pictures';
    private const CURRENTLY_READING_BOOK_READ_STATUS_TYPE_ID = 1; // todo: replace with enum
    private const FINISHED_BOOK_READ_STATUS_TYPE_ID = 2;

    protected $fillable = [
        'name',
        'email',
        'password',
        'is_password_set',
        'google_sub',
        'apple_sub',
        'profile_picture',
        'language',
        'register_ip',
        'new_email',
    ];
    protected $hidden = [
        'password',
        'remember_token',
    ];
    protected $casts = [
        'email_verified_at' => 'datetime',
        'has_done_tutorial' => 'bool',
    ];
    public bool $notifyNewEmail = false;

    public function bookLists(): HasMany
    {
        return $this->hasMany(BookList::class);
    }

    public function bookLikesAndDislikes(): HasMany
    {
        return $this->hasMany(BookLike::class);
    }

    public function bookLikes(): HasMany
    {
        return $this->hasMany(BookLike::class)->where('isPositive', 1);
    }

    public function bookShares(): HasMany
    {
        return $this->hasMany(BookShare::class);
    }

    public function booksWithReadStatus(): HasMany
    {
        return $this->hasMany(BookReadStatus::class);
    }

    public function currentlyReadingBookStatuses(): HasMany
    {
        return $this->hasMany(BookReadStatus::class)
            ->where('book_read_status_type_id', self::CURRENTLY_READING_BOOK_READ_STATUS_TYPE_ID);
    }

    public function finishedBookStatuses(): HasMany
    {
        return $this->hasMany(BookReadStatus::class)
            ->where('book_read_status_type_id', self::FINISHED_BOOK_READ_STATUS_TYPE_ID);
    }

    public function recommendations(): HasMany
    {
        return $this->hasMany(Recommendation::class)->orderBy('score', 'desc');
    }

    public function authorsFollowed(): BelongsToMany
    {
        return $this->belongsToMany(Author::class, 'author_follows');
    }

    public function bookViews(): HasMany
    {
        return $this->hasMany(BookView::class);
    }

    public function achievements(): BelongsToMany
    {
        return $this->belongsToMany(Achievement::class);
    }

    public function activities(): HasMany
    {
        return $this->hasMany(UserActivity::class);
    }

    public function aiConversations(): HasMany
    {
        return $this->hasMany(AiConversation::class);
    }

    public function readingActivities(): HasMany
    {
        return $this->hasMany(ReadingActivity::class);
    }

    public function aiConsumedResources(): HasMany
    {
        return $this->hasMany(AiConsumedResource::class);
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new EmailVerificationNotification());
    }

    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new PasswordResetNotification($token));
    }

    public function routeNotificationForMail(): string
    {
        return $this->notifyNewEmail ? $this->new_email : $this->email;
    }

    public function getProfilePicturePath(): ?string
    {
        if ($this->profile_picture === null) {
            return null;
        }

        return self::PROFILE_PICTURES_DIRECTORY . '/' . $this->profile_picture;
    }

    public function getProfilePictureUrl(): ?string
    {
        if ($this->profile_picture === null) {
            return null;
        }

        $path = $this->getProfilePicturePath();

        return Storage::disk('public')->url($path);
    }

    public function lastActiveDay(): ?Carbon
    {
        return $this->activities()->latest('date')->first()?->date;
    }

    /**
     * @return Collection<int, Book>
     */
    public function likedBooks(): Collection
    {
        return $this->bookLikes->map->book;
    }

    /**
     * @return Collection<int, Book>
     */
    public function sharedBooks(): Collection
    {
        return $this->bookShares->map->book;
    }

    /**
     * @return Collection<int, Book>
     */
    public function finishedBooks(): Collection
    {
        return $this->finishedBookStatuses->map->book;
    }

    /**
     * @return Collection<int, Book>
     */
    public function viewedBooks(): Collection
    {
        return $this->bookViews->map->book;
    }
}
