<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\BookReadStatusType
 *
 * @property int $id
 * @property string $name
 */
final class BookReadStatusType extends Model
{
    public const WANT_TO_READ = 3;

    public $timestamps = false;

    public function bookReadStatuses(): HasMany
    {
        return $this->hasMany(BookReadStatus::class);
    }
}
