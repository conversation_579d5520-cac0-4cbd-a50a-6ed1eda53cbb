<?php declare(strict_types=1);

namespace App\Models;

use App\Enums\BookReadStatusType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\BookReadStatus
 *
 * @property int $id
 * @property int $book_read_status_type_id
 * @property int $book_id
 * @property int $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class BookReadStatus extends Model
{
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public function getStatusType(): ?BookReadStatusType
    {
        return BookReadStatusType::fromId($this->book_read_status_type_id);
    }

    public function setStatusType(BookReadStatusType $statusType): void
    {
        $this->book_read_status_type_id = $statusType->getId();
    }
}
