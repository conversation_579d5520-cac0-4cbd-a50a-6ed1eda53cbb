import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Author from '$lib/domain/Author';

export default class AuthorsApiClient extends MainApiClient {
    public async index(query: string): Promise<IndexResponse> {
        return await this.get('/authors', {query});
    }

    public async indexAudiobooks(query: string): Promise<IndexAudiobooksResponse> {
        return await this.get('/authors/audiobooks', {query});
    }

    public async followed(query: string): Promise<FollowedResponse> {
        return await this.get('/authors/followed', {query});
    }

    public async show(id: string): Promise<ShowResponse> {
        return await this.get(`/authors/${id}`);
    }

    public async follow(id: string): Promise<FollowResponse> {
        return await this.post(`/authors/${id}/follow`);
    }

    public async unfollow(id: string): Promise<UnfollowResponse> {
        return await this.post(`/authors/${id}/unfollow`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: string[];
}

interface IndexAudiobooksResponse extends BaseApiResponse {
    data: string[];
}

interface FollowedResponse extends BaseApiResponse {
    data: Author[];
}

interface ShowResponse extends BaseApiResponse {
    data: Author;
}

interface FollowResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface UnfollowResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}
