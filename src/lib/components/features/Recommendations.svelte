<script lang="ts">
    import ReadingActivityModal from '$lib/components/features/ReadingActivityModal.svelte';
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import type Book from '$lib/domain/Book';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import type Recommendations from '$lib/domain/Recommendations';
    import {t} from '$lib/localization/Localization';

    export let recommendations: Recommendations;

    let showEditModal = false;
    let editingActivity: ReadingActivity | null = null;
    let editingBook: Book | null = null;

    function handleEditActivity(activity: ReadingActivity | null, book: Book) {
        editingActivity = activity;
        editingBook = book;
        showEditModal = true;
    }

    function handleActivitySaved(updatedActivity?: ReadingActivity) {
        showEditModal = false;
        const wasCreatingNew = editingActivity === null;
        const editingActivityId = editingActivity?.id;
        editingActivity = null;

        if (editingBook && updatedActivity) {
            const bookIndex = recommendations.currentlyReading.findIndex((book) => book.uuid === editingBook.uuid);
            if (bookIndex !== -1) {
                if (wasCreatingNew) {
                    recommendations.currentlyReading[bookIndex].readingActivity = [
                        updatedActivity,
                        ...recommendations.currentlyReading[bookIndex].readingActivity,
                    ];
                } else {
                    const activityIndex = recommendations.currentlyReading[bookIndex].readingActivity.findIndex(
                        (activity) => activity.id === editingActivityId,
                    );
                    if (activityIndex !== -1) {
                        recommendations.currentlyReading[bookIndex].readingActivity[activityIndex] = updatedActivity;
                    } else if (recommendations.currentlyReading[bookIndex].readingActivity.length > 0) {
                        recommendations.currentlyReading[bookIndex].readingActivity[0] = updatedActivity;
                    } else {
                        recommendations.currentlyReading[bookIndex].readingActivity = [updatedActivity];
                    }
                }

                recommendations = {...recommendations};
            }
        }

        editingBook = null;
    }
</script>

{#if recommendations.currentlyReading.length > 0}
    <Container title={$t('recommendations.continueReading')}>
        <BookListUI
            books={recommendations.currentlyReading}
            onEditActivity={handleEditActivity}
            showProgressForAll={true}
        />
        <ReadingActivityModal
            bind:open={showEditModal}
            book={editingBook}
            editingActivity={editingActivity}
            isEditing={editingActivity !== null}
            modalTriggeredByStatusChange={false}
            onActivitySaved={handleActivitySaved}
        />
    </Container>
{/if}
{#if recommendations.forYou.length > 0}
    <Container title={$t('recommendations.forYou')}>
        <BookListUI books={recommendations.forYou} />
    </Container>
{/if}
