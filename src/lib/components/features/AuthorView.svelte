<script lang="ts">
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import type Author from '$lib/domain/Author';
    import {t} from '$lib/localization/Localization';
    import BookSearchFiltersService from '$lib/services/BookSearchFiltersService';

    export let author: Author;
</script>

<Container extraClass="space-x-8 lg:flex">
    <div class="lg:w-4/5 lg:flex-col">
        <h1 class="text-3xl font-bold">{author.name}</h1>
        <div class="mt-6 space-y-6">
            {#if author.subjects.length > 0}
                <div>
                    <span class="font-semibold">{$t('book.subjects')}:</span>
                    {#each author.subjects as subject, index (index)}
                        <button type="button" on:click={() => BookSearchFiltersService.goToSubjectSearch(subject)}>
                            {subject}
                        </button>
                        {#if index < author.subjects.length - 1}
                            <span>, </span>
                        {/if}
                    {/each}
                </div>
            {/if}
        </div>
    </div>
</Container>
<Container>
    <h1 class="text-2xl font-bold">{$t('author.books')}</h1>
    <div class="mt-5 space-y-4">
        <BookListUI books={author.books} />
    </div>
</Container>
