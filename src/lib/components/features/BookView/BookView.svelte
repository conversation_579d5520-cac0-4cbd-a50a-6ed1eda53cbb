<script lang="ts">
    import BooksApiClient from '$lib/api/BooksApiClient';
    import BookActions from '$lib/components/features/BookView/BookActions.svelte';
    import BookTabs from '$lib/components/features/BookView/BookTabs.svelte';
    import Container from '$lib/components/layout/body/Container.svelte';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import type Book from '$lib/domain/Book';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    let bookTabsComponent: BookTabs;

    async function getSimilarBooks(bookId: string): Promise<{
        paid: Book[];
        free: Book[];
    }> {
        const booksApiClient = new BooksApiClient();
        const response = await booksApiClient.similar(bookId);

        return response.ok ? response.data : {paid: [], free: []};
    }

    let getSimilarBooksPromise;
    $: uuid = book.uuid; // hack to only refresh getSimilarBooksPromise when uuid changes and not other book properties
    $: getSimilarBooksPromise = getSimilarBooks(uuid);
</script>

<Container extraClass="space-y-4 lg:space-y-0 lg:space-x-8 lg:flex">
    <div class="mx-auto w-1/2 sm:w-1/3 lg:w-1/5 lg:flex-col">
        <img src={book.cover} class="mb-4 w-full" alt="Book cover" />
        <div class="hidden lg:flex lg:justify-center">
            <div class="inline-block">
                <BookActions book={book} bind:bookTabsComponent={bookTabsComponent} />
            </div>
        </div>
    </div>
    <div class="lg:w-4/5 lg:flex-col">
        {#if book.audiobookId}
            <div class="mb-4 text-center lg:text-left">
                <Button href={AppRoutes.audiobook(book.audiobookId)} title={$t('book.audiobook')} />
            </div>
        {/if}
        <div class="mb-4 flex justify-center lg:hidden">
            <div class="inline-block">
                <BookActions book={book} bind:bookTabsComponent={bookTabsComponent} />
            </div>
        </div>
        <BookTabs bind:this={bookTabsComponent} book={book} />
    </div>
</Container>
{#await getSimilarBooksPromise}
    <Container>
        <div class="mt-5 space-y-4">
            <SpinnerSvg svgClass="w-8 h-8 mx-auto" />
        </div>
    </Container>
{:then data}
    {#if data.paid.length > 0}
        <Container>
            <h1 class="text-2xl font-bold">{$t('book.similarBooks')}</h1>
            <div class="mt-5 space-y-4">
                <BookListUI books={data.paid} />
            </div>
        </Container>
    {/if}
    {#if data.free.length > 0}
        <Container>
            <h1 class="text-2xl font-bold">{$t('book.similarFreeBooks')}</h1>
            <div class="mt-5 space-y-4">
                <BookListUI books={data.free} />
            </div>
        </Container>
    {/if}
{/await}
