import {goto} from '$app/navigation';
import type Language from '$lib/domain/Language';
import BookSearchFiltersStores, {resetSearchFilters} from '$lib/stores/BookSearchFiltersStores';
import AppRoutes from '$routes/AppRoutes';

export default {
    goToLanguageSearch: async function (language: Language): Promise<void> {
        resetSearchFilters();
        BookSearchFiltersStores.language.set({
            label: language.label || '',
            value: language.value,
            index: 0,
        });

        await goto(AppRoutes.search);
    },
    goToSubjectSearch: async function (subject: string): Promise<void> {
        resetSearchFilters();
        BookSearchFiltersStores.subject.set({
            label: subject,
            value: subject,
            index: 0,
        });

        await goto(AppRoutes.search);
    },
    goToIsbnSearch: async function (isbn: string): Promise<void> {
        resetSearchFilters();
        BookSearchFiltersStores.isbn.set(isbn);

        await goto(AppRoutes.search);
    },
};
