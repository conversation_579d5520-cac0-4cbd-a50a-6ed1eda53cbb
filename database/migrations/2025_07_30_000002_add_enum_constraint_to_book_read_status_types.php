<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('book_read_status_types', function (Blueprint $table) {
            $table->enum('name', ['currentlyReading', 'read', 'wantToRead', 'dropped'])->change();
        });
    }

    public function down(): void
    {
        Schema::table('book_read_status_types', function (Blueprint $table) {
            $table->string('name')->change();
        });
    }
};
